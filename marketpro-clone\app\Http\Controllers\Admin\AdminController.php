<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\Service;
use App\Models\CaseStudy;
use App\Models\User;
use Illuminate\Http\Request;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    public function dashboard()
    {
        $stats = [
            'blog_posts' => BlogPost::count(),
            'published_posts' => BlogPost::published()->count(),
            'services' => Service::active()->count(),
            'case_studies' => CaseStudy::active()->count(),
            'users' => User::count(),
            'admins' => User::admins()->count(),
        ];

        $recent_posts = BlogPost::with('author', 'category')
            ->latest()
            ->take(5)
            ->get();

        return view('admin.dashboard', compact('stats', 'recent_posts'));
    }
}
