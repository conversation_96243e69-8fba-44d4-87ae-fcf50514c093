<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class CaseStudy extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'title',
        'slug',
        'client_name',
        'client_industry',
        'client_logo',
        'overview',
        'problem',
        'strategy',
        'results',
        'featured_image',
        'images',
        'metrics',
        'tags',
        'category_id',
        'is_featured',
        'is_active',
        'sort_order',
        'meta_title',
        'meta_description'
    ];

    protected $casts = [
        'images' => 'array',
        'metrics' => 'array',
        'tags' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    public function scopeByIndustry($query, $industry)
    {
        return $query->where('client_industry', $industry);
    }

    // Accessors
    public function getFormattedMetricsAttribute()
    {
        if (!$this->metrics) {
            return [];
        }

        return collect($this->metrics)->map(function ($metric) {
            return [
                'label' => $metric['label'] ?? '',
                'before' => $metric['before'] ?? '',
                'after' => $metric['after'] ?? '',
                'improvement' => $metric['improvement'] ?? ''
            ];
        });
    }
}
