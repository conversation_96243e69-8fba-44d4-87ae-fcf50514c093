<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamMember extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'position',
        'bio',
        'photo',
        'email',
        'phone',
        'linkedin_url',
        'twitter_url',
        'facebook_url',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getSocialLinksAttribute()
    {
        $links = [];

        if ($this->linkedin_url) {
            $links['linkedin'] = $this->linkedin_url;
        }

        if ($this->twitter_url) {
            $links['twitter'] = $this->twitter_url;
        }

        if ($this->facebook_url) {
            $links['facebook'] = $this->facebook_url;
        }

        return $links;
    }
}
