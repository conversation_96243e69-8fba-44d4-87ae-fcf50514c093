<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Testimonial extends Model
{
    use HasFactory;

    protected $fillable = [
        'content',
        'client_name',
        'client_position',
        'client_company',
        'client_photo',
        'rating',
        'is_featured',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', '>=', $rating);
    }

    // Accessors
    public function getStarsAttribute()
    {
        return str_repeat('★', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }

    public function getClientFullNameAttribute()
    {
        $name = $this->client_name;

        if ($this->client_position && $this->client_company) {
            $name .= ', ' . $this->client_position . ' at ' . $this->client_company;
        } elseif ($this->client_position) {
            $name .= ', ' . $this->client_position;
        } elseif ($this->client_company) {
            $name .= ', ' . $this->client_company;
        }

        return $name;
    }
}
