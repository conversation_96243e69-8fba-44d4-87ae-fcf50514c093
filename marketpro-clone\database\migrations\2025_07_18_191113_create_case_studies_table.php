<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('case_studies', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->string('client_name');
            $table->string('client_industry')->nullable();
            $table->string('client_logo')->nullable();
            $table->text('overview');
            $table->longText('problem');
            $table->longText('strategy');
            $table->longText('results');
            $table->string('featured_image')->nullable();
            $table->json('images')->nullable(); // Array of additional images
            $table->json('metrics')->nullable(); // Before/after metrics
            $table->json('tags')->nullable(); // Array of tags
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('set null');
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('case_studies');
    }
};
