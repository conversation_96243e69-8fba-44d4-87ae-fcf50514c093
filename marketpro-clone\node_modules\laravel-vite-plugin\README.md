# Laravel Vite Plugin

<a href="https://github.com/laravel/vite-plugin/actions"><img src="https://github.com/laravel/vite-plugin/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://www.npmjs.com/package/laravel-vite-plugin"><img src="https://img.shields.io/npm/dt/laravel-vite-plugin" alt="Total Downloads"></a>
<a href="https://www.npmjs.com/package/laravel-vite-plugin"><img src="https://img.shields.io/npm/v/laravel-vite-plugin" alt="Latest Stable Version"></a>
<a href="https://www.npmjs.com/package/laravel-vite-plugin"><img src="https://img.shields.io/npm/l/laravel-vite-plugin" alt="License"></a>

## Introduction

[Vite](https://vitejs.dev) is a modern frontend build tool that provides an extremely fast development environment and bundles your code for production.

This plugin configures Vite for use with a Laravel backend server.

## Official Documentation

Documentation for the Laravel Vite plugin can be found on the [Laravel website](https://laravel.com/docs/vite).

## Contributing

Thank you for considering contributing to the Laravel Vite plugin! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

Please review [our security policy](https://github.com/laravel/vite-plugin/security/policy) on how to report security vulnerabilities.

## License

The Laravel Vite plugin is open-sourced software licensed under the [MIT license](LICENSE.md).
