import{s as setInnerHTML}from"./utils.min.mjs";const paramsList=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function isObject(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function extend(e,t){const i=["__proto__","constructor","prototype"];Object.keys(t).filter((e=>i.indexOf(e)<0)).forEach((i=>{void 0===e[i]?e[i]=t[i]:isObject(t[i])&&isObject(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:extend(e[i],t[i]):e[i]=t[i]}))}function needsNavigation(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function needsPagination(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function needsScrollbar(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function uniqueClasses(e){void 0===e&&(e="");const t=e.split(" ").map((e=>e.trim())).filter((e=>!!e)),i=[];return t.forEach((e=>{i.indexOf(e)<0&&i.push(e)})),i.join(" ")}function attrToProp(e){return void 0===e&&(e=""),e.replace(/-[a-z]/g,(e=>e.toUpperCase().replace("-","")))}function wrapperClass(e){return void 0===e&&(e=""),e?e.includes("swiper-wrapper")?e:`swiper-wrapper ${e}`:"swiper-wrapper"}function updateSwiper(e){let{swiper:t,slides:i,passedParams:l,changedParams:n,nextEl:o,prevEl:r,scrollbarEl:s,paginationEl:a}=e;const d=n.filter((e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e)),{params:c,pagination:p,navigation:u,scrollbar:v,virtual:g,thumbs:b}=t;let f,w,_,h,m,S,E,C;n.includes("thumbs")&&l.thumbs&&l.thumbs.swiper&&!l.thumbs.swiper.destroyed&&c.thumbs&&(!c.thumbs.swiper||c.thumbs.swiper.destroyed)&&(f=!0),n.includes("controller")&&l.controller&&l.controller.control&&c.controller&&!c.controller.control&&(w=!0),n.includes("pagination")&&l.pagination&&(l.pagination.el||a)&&(c.pagination||!1===c.pagination)&&p&&!p.el&&(_=!0),n.includes("scrollbar")&&l.scrollbar&&(l.scrollbar.el||s)&&(c.scrollbar||!1===c.scrollbar)&&v&&!v.el&&(h=!0),n.includes("navigation")&&l.navigation&&(l.navigation.prevEl||r)&&(l.navigation.nextEl||o)&&(c.navigation||!1===c.navigation)&&u&&!u.prevEl&&!u.nextEl&&(m=!0);const x=e=>{t[e]&&(t[e].destroy(),"navigation"===e?(t.isElement&&(t[e].prevEl.remove(),t[e].nextEl.remove()),c[e].prevEl=void 0,c[e].nextEl=void 0,t[e].prevEl=void 0,t[e].nextEl=void 0):(t.isElement&&t[e].el.remove(),c[e].el=void 0,t[e].el=void 0))};if(n.includes("loop")&&t.isElement&&(c.loop&&!l.loop?S=!0:!c.loop&&l.loop?E=!0:C=!0),d.forEach((e=>{if(isObject(c[e])&&isObject(l[e]))Object.assign(c[e],l[e]),"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e||!("enabled"in l[e])||l[e].enabled||x(e);else{const t=l[e];!0!==t&&!1!==t||"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e?c[e]=l[e]:!1===t&&x(e)}})),d.includes("controller")&&!w&&t.controller&&t.controller.control&&c.controller&&c.controller.control&&(t.controller.control=c.controller.control),n.includes("children")&&i&&g&&c.virtual.enabled?(g.slides=i,g.update(!0)):n.includes("virtual")&&g&&c.virtual.enabled&&(i&&(g.slides=i),g.update(!0)),n.includes("children")&&i&&c.loop&&(C=!0),f){b.init()&&b.update(!0)}w&&(t.controller.control=c.controller.control),_&&(!t.isElement||a&&"string"!=typeof a||(a=document.createElement("div"),a.classList.add("swiper-pagination"),a.part.add("pagination"),t.el.appendChild(a)),a&&(c.pagination.el=a),p.init(),p.render(),p.update()),h&&(!t.isElement||s&&"string"!=typeof s||(s=document.createElement("div"),s.classList.add("swiper-scrollbar"),s.part.add("scrollbar"),t.el.appendChild(s)),s&&(c.scrollbar.el=s),v.init(),v.updateSize(),v.setTranslate()),m&&(t.isElement&&(o&&"string"!=typeof o||(o=document.createElement("div"),o.classList.add("swiper-button-next"),setInnerHTML(o,t.hostEl.constructor.nextButtonSvg),o.part.add("button-next"),t.el.appendChild(o)),r&&"string"!=typeof r||(r=document.createElement("div"),r.classList.add("swiper-button-prev"),setInnerHTML(r,t.hostEl.constructor.prevButtonSvg),r.part.add("button-prev"),t.el.appendChild(r))),o&&(c.navigation.nextEl=o),r&&(c.navigation.prevEl=r),u.init(),u.update()),n.includes("allowSlideNext")&&(t.allowSlideNext=l.allowSlideNext),n.includes("allowSlidePrev")&&(t.allowSlidePrev=l.allowSlidePrev),n.includes("direction")&&t.changeDirection(l.direction,!1),(S||C)&&t.loopDestroy(),(E||C)&&t.loopCreate(),t.update()}export{needsPagination as a,needsScrollbar as b,attrToProp as c,uniqueClasses as d,extend as e,isObject as i,needsNavigation as n,paramsList as p,updateSwiper as u,wrapperClass as w};
//# sourceMappingURL=update-swiper.min.mjs.map