/**
 * Swiper Custom Element 11.2.10
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: June 28, 2025
 */

import{S as Swiper}from"./shared/swiper-core.min.mjs";import{p as paramsList,n as needsNavigation,a as needsPagination,b as needsScrollbar,u as updateSwiper,c as attrToProp}from"./shared/update-swiper.min.mjs";import{g as getParams}from"./shared/get-element-params.min.mjs";import{s as setInnerHTML}from"./shared/utils.min.mjs";const SwiperCSS=":host{--swiper-theme-color:#007aff}:host{position:relative;display:block;margin-left:auto;margin-right:auto;z-index:1}.swiper{width:100%;height:100%;margin-left:auto;margin-right:auto;position:relative;overflow:hidden;list-style:none;padding:0;z-index:1;display:block}.swiper-vertical>.swiper-wrapper{flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:flex;transition-property:transform;transition-timing-function:var(--swiper-wrapper-transition-timing-function,initial);box-sizing:content-box}.swiper-android ::slotted(swiper-slide),.swiper-ios ::slotted(swiper-slide),.swiper-wrapper{transform:translate3d(0px,0,0)}.swiper-horizontal{touch-action:pan-y}.swiper-vertical{touch-action:pan-x}::slotted(swiper-slide){flex-shrink:0;width:100%;height:100%;position:relative;transition-property:transform;display:block}::slotted(.swiper-slide-invisible-blank){visibility:hidden}.swiper-autoheight,.swiper-autoheight ::slotted(swiper-slide){height:auto}.swiper-autoheight .swiper-wrapper{align-items:flex-start;transition-property:transform,height}.swiper-backface-hidden ::slotted(swiper-slide){transform:translateZ(0);-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-3d.swiper-css-mode .swiper-wrapper{perspective:1200px}.swiper-3d .swiper-wrapper{transform-style:preserve-3d}.swiper-3d{perspective:1200px}.swiper-3d .swiper-cube-shadow,.swiper-3d ::slotted(swiper-slide){transform-style:preserve-3d}.swiper-css-mode>.swiper-wrapper{overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{display:none}.swiper-css-mode ::slotted(swiper-slide){scroll-snap-align:start start}.swiper-css-mode.swiper-horizontal>.swiper-wrapper{scroll-snap-type:x mandatory}.swiper-css-mode.swiper-vertical>.swiper-wrapper{scroll-snap-type:y mandatory}.swiper-css-mode.swiper-free-mode>.swiper-wrapper{scroll-snap-type:none}.swiper-css-mode.swiper-free-mode ::slotted(swiper-slide){scroll-snap-align:none}.swiper-css-mode.swiper-centered>.swiper-wrapper::before{content:'';flex-shrink:0;order:9999}.swiper-css-mode.swiper-centered ::slotted(swiper-slide){scroll-snap-align:center center;scroll-snap-stop:always}.swiper-css-mode.swiper-centered.swiper-horizontal ::slotted(swiper-slide):first-child{margin-inline-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper::before{height:100%;min-height:1px;width:var(--swiper-centered-offset-after)}.swiper-css-mode.swiper-centered.swiper-vertical ::slotted(swiper-slide):first-child{margin-block-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper::before{width:100%;min-width:1px;height:var(--swiper-centered-offset-after)}",SwiperSlideCSS="::slotted(.swiper-slide-shadow),::slotted(.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-top){position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}::slotted(.swiper-slide-shadow){background:rgba(0,0,0,.15)}::slotted(.swiper-slide-shadow-left){background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-right){background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-top){background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-bottom){background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-lazy-preloader{animation:swiper-preloader-spin 1s infinite linear;width:42px;height:42px;position:absolute;left:50%;top:50%;margin-left:-21px;margin-top:-21px;z-index:10;transform-origin:50%;box-sizing:border-box;border:4px solid var(--swiper-preloader-color,var(--swiper-theme-color));border-radius:50%;border-top-color:transparent}@keyframes swiper-preloader-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-top){z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-top){z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}::slotted(.swiper-zoom-container){width:100%;height:100%;display:flex;justify-content:center;align-items:center;text-align:center}::slotted(.swiper-zoom-container)>canvas,::slotted(.swiper-zoom-container)>img,::slotted(.swiper-zoom-container)>svg{max-width:100%;max-height:100%;object-fit:contain}";class DummyHTMLElement{}const ClassToExtend="undefined"==typeof window||"undefined"==typeof HTMLElement?DummyHTMLElement:HTMLElement,arrowSvg='<svg width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.38296 20.0762C0.111788 19.805 0.111788 19.3654 0.38296 19.0942L9.19758 10.2796L0.38296 1.46497C0.111788 1.19379 0.111788 0.754138 0.38296 0.482966C0.654131 0.211794 1.09379 0.211794 1.36496 0.482966L10.4341 9.55214C10.8359 9.9539 10.8359 10.6053 10.4341 11.007L1.36496 20.0762C1.09379 20.3474 0.654131 20.3474 0.38296 20.0762Z" fill="currentColor"/></svg>\n    ',addStyle=(e,s)=>{if("undefined"!=typeof CSSStyleSheet&&e.adoptedStyleSheets){const t=new CSSStyleSheet;t.replaceSync(s),e.adoptedStyleSheets=[t]}else{const t=document.createElement("style");t.rel="stylesheet",t.textContent=s,e.appendChild(t)}};class SwiperContainer extends ClassToExtend{constructor(){super(),this.attachShadow({mode:"open"})}static get nextButtonSvg(){return arrowSvg}static get prevButtonSvg(){return arrowSvg.replace("/></svg>",' transform-origin="center" transform="rotate(180)"/></svg>')}cssStyles(){return[SwiperCSS,...this.injectStyles&&Array.isArray(this.injectStyles)?this.injectStyles:[]].join("\n")}cssLinks(){return this.injectStylesUrls||[]}calcSlideSlots(){const e=this.slideSlots||0,s=[...this.querySelectorAll("[slot^=slide-]")].map((e=>parseInt(e.getAttribute("slot").split("slide-")[1],10)));if(this.slideSlots=s.length?Math.max(...s)+1:0,this.rendered)if(this.slideSlots>e)for(let s=e;s<this.slideSlots;s+=1){const e=document.createElement("swiper-slide");e.setAttribute("part",`slide slide-${s+1}`);const t=document.createElement("slot");t.setAttribute("name",`slide-${s+1}`),e.appendChild(t),this.shadowRoot.querySelector(".swiper-wrapper").appendChild(e)}else if(this.slideSlots<e){const e=this.swiper.slides;for(let s=e.length-1;s>=0;s-=1)s>this.slideSlots&&e[s].remove()}}render(){if(this.rendered)return;this.calcSlideSlots();let e=this.cssStyles();this.slideSlots>0&&(e=e.replace(/::slotted\(([a-z-0-9.]*)\)/g,"$1")),e.length&&addStyle(this.shadowRoot,e),this.cssLinks().forEach((e=>{if(this.shadowRoot.querySelector(`link[href="${e}"]`))return;const s=document.createElement("link");s.rel="stylesheet",s.href=e,this.shadowRoot.appendChild(s)}));const s=document.createElement("div");s.classList.add("swiper"),s.part="container",setInnerHTML(s,`\n      <slot name="container-start"></slot>\n      <div class="swiper-wrapper" part="wrapper">\n        <slot></slot>\n        ${Array.from({length:this.slideSlots}).map(((e,s)=>`\n        <swiper-slide part="slide slide-${s}">\n          <slot name="slide-${s}"></slot>\n        </swiper-slide>\n        `)).join("")}\n      </div>\n      <slot name="container-end"></slot>\n      ${needsNavigation(this.passedParams)?`\n        <div part="button-prev" class="swiper-button-prev">${this.constructor.prevButtonSvg}</div>\n        <div part="button-next" class="swiper-button-next">${this.constructor.nextButtonSvg}</div>\n      `:""}\n      ${needsPagination(this.passedParams)?'\n        <div part="pagination" class="swiper-pagination"></div>\n      ':""}\n      ${needsScrollbar(this.passedParams)?'\n        <div part="scrollbar" class="swiper-scrollbar"></div>\n      ':""}\n    `),this.shadowRoot.appendChild(s),this.rendered=!0}initialize(){var e=this;if(this.swiper&&this.swiper.initialized)return;const{params:s,passedParams:t}=getParams(this);this.swiperParams=s,this.passedParams=t,delete this.swiperParams.init,this.render(),this.swiper=new Swiper(this.shadowRoot.querySelector(".swiper"),{...s.virtual?{}:{observer:!0},...s,touchEventsTarget:"container",onAny:function(t){"observerUpdate"===t&&e.calcSlideSlots();const i=s.eventsPrefix?`${s.eventsPrefix}${t.toLowerCase()}`:t.toLowerCase();for(var r=arguments.length,a=new Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];const n=new CustomEvent(i,{detail:a,bubbles:"hashChange"!==t,cancelable:!0});e.dispatchEvent(n)}})}connectedCallback(){this.swiper&&this.swiper.initialized&&this.nested&&this.closest("swiper-slide")&&this.closest("swiper-slide").swiperLoopMoveDOM||!1!==this.init&&"false"!==this.getAttribute("init")&&this.initialize()}disconnectedCallback(){this.nested&&this.closest("swiper-slide")&&this.closest("swiper-slide").swiperLoopMoveDOM||this.swiper&&this.swiper.destroy&&this.swiper.destroy()}updateSwiperOnPropChange(e,s){const{params:t,passedParams:i}=getParams(this,e,s);this.passedParams=i,this.swiperParams=t,this.swiper&&this.swiper.params[e]===s||updateSwiper({swiper:this.swiper,passedParams:this.passedParams,changedParams:[attrToProp(e)],..."navigation"===e&&i[e]?{prevEl:".swiper-button-prev",nextEl:".swiper-button-next"}:{},..."pagination"===e&&i[e]?{paginationEl:".swiper-pagination"}:{},..."scrollbar"===e&&i[e]?{scrollbarEl:".swiper-scrollbar"}:{}})}attributeChangedCallback(e,s,t){this.swiper&&this.swiper.initialized&&("true"===s&&null===t&&(t=!1),this.updateSwiperOnPropChange(e,t))}static get observedAttributes(){return paramsList.filter((e=>e.includes("_"))).map((e=>e.replace(/[A-Z]/g,(e=>`-${e}`)).replace("_","").toLowerCase()))}}paramsList.forEach((e=>{"init"!==e&&(e=e.replace("_",""),Object.defineProperty(SwiperContainer.prototype,e,{configurable:!0,get(){return(this.passedParams||{})[e]},set(s){this.passedParams||(this.passedParams={}),this.passedParams[e]=s,this.swiper&&this.swiper.initialized&&this.updateSwiperOnPropChange(e,s)}}))}));class SwiperSlide extends ClassToExtend{constructor(){super(),this.attachShadow({mode:"open"})}render(){const e=this.lazy||""===this.getAttribute("lazy")||"true"===this.getAttribute("lazy");if(addStyle(this.shadowRoot,SwiperSlideCSS),this.shadowRoot.appendChild(document.createElement("slot")),e){const e=document.createElement("div");e.classList.add("swiper-lazy-preloader"),e.part.add("preloader"),this.shadowRoot.appendChild(e)}}initialize(){this.render()}connectedCallback(){this.swiperLoopMoveDOM||this.initialize()}}const register=()=>{"undefined"!=typeof window&&(window.customElements.get("swiper-container")||window.customElements.define("swiper-container",SwiperContainer),window.customElements.get("swiper-slide")||window.customElements.define("swiper-slide",SwiperSlide))};"undefined"!=typeof window&&(window.SwiperElementRegisterParams=e=>{paramsList.push(...e)});export{SwiperContainer,SwiperSlide,register};
//# sourceMappingURL=swiper-element.min.mjs.map