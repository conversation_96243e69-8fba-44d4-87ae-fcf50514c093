/* Basic CSS for testing - Tailwind will be added later */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
nav {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2563eb;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #2563eb;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
    text-align: center;
    padding: 2rem;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #1f2937;
}

.hero p {
    font-size: 1.25rem;
    color: #6b7280;
    margin-bottom: 2rem;
    max-width: 600px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    margin: 0 10px;
}

.btn-primary {
    background: #2563eb;
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

.btn-outline {
    border: 2px solid #2563eb;
    color: #2563eb;
}

.btn-outline:hover {
    background: #2563eb;
    color: white;
}

/* Sections */
.section {
    padding: 4rem 0;
}

.section h2 {
    font-size: 2.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 3rem;
    color: #1f2937;
}

/* Grid */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Cards */
.card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Footer */
footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0;
    text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.5rem;
    }

    .nav-links {
        display: none;
    }

    .grid-3 {
        grid-template-columns: 1fr;
    }
}

/* Custom Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Base Styles */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        font-family: 'Inter', sans-serif;
    }
}

/* Component Styles */
@layer components {
    /* Navigation Styles */
    .nav-link {
        @apply text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-300 relative;
    }

    .nav-link.active {
        @apply text-blue-600;
    }

    .nav-link.active::after {
        content: '';
        @apply absolute bottom-0 left-0 w-full h-0.5 bg-blue-600;
    }

    .dropdown-link {
        @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors duration-200;
    }

    .mobile-nav-link {
        @apply block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors duration-300;
    }

    .mobile-nav-link.active {
        @apply text-blue-600 bg-blue-50;
    }

    /* Button Styles */
    .btn-primary {
        @apply bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
    }

    .btn-secondary {
        @apply bg-gray-100 hover:bg-gray-200 text-gray-900 px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
    }

    .btn-outline {
        @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
    }

    /* Card Styles */
    .card {
        @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden;
    }

    .card-hover {
        @apply transform hover:scale-105 transition-transform duration-300;
    }

    /* Section Styles */
    .section-padding {
        @apply py-16 lg:py-24;
    }

    .container-custom {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    /* Typography */
    .heading-xl {
        @apply text-4xl lg:text-6xl font-bold text-gray-900 leading-tight;
    }

    .heading-lg {
        @apply text-3xl lg:text-5xl font-bold text-gray-900 leading-tight;
    }

    .heading-md {
        @apply text-2xl lg:text-4xl font-bold text-gray-900 leading-tight;
    }

    .heading-sm {
        @apply text-xl lg:text-2xl font-semibold text-gray-900;
    }

    .text-lead {
        @apply text-lg lg:text-xl text-gray-600 leading-relaxed;
    }

    /* Form Styles */
    .form-input {
        @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300;
    }

    .form-textarea {
        @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    /* Animation Classes */
    .fade-in-up {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease-out;
    }

    .fade-in-up.animate {
        opacity: 1;
        transform: translateY(0);
    }

    .fade-in-left {
        opacity: 0;
        transform: translateX(-30px);
        transition: all 0.6s ease-out;
    }

    .fade-in-left.animate {
        opacity: 1;
        transform: translateX(0);
    }

    .fade-in-right {
        opacity: 0;
        transform: translateX(30px);
        transition: all 0.6s ease-out;
    }

    .fade-in-right.animate {
        opacity: 1;
        transform: translateX(0);
    }

    /* Gradient Backgrounds */
    .gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .gradient-secondary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .gradient-blue {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    /* Hero Section */
    .hero-bg {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    }

    /* Stats Counter */
    .stats-counter {
        @apply text-4xl lg:text-5xl font-bold text-blue-600;
    }

    /* Service Card */
    .service-card {
        @apply bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group;
    }

    .service-icon {
        @apply w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors duration-300;
    }

    .service-icon svg {
        @apply w-8 h-8 text-blue-600 group-hover:text-white transition-colors duration-300;
    }

    /* Testimonial Card */
    .testimonial-card {
        @apply bg-white p-8 rounded-xl shadow-lg relative;
    }

    .testimonial-quote {
        @apply absolute -top-4 -left-4 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center;
    }

    /* Blog Card */
    .blog-card {
        @apply bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300;
    }

    .blog-image {
        @apply w-full h-48 object-cover;
    }

    .blog-content {
        @apply p-6;
    }

    .blog-meta {
        @apply flex items-center text-sm text-gray-500 mb-3;
    }

    .blog-title {
        @apply text-xl font-semibold text-gray-900 mb-3 hover:text-blue-600 transition-colors duration-300;
    }

    .blog-excerpt {
        @apply text-gray-600 mb-4;
    }

    .blog-read-more {
        @apply text-blue-600 font-medium hover:text-blue-700 transition-colors duration-300;
    }
}