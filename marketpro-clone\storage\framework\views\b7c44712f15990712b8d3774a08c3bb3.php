<nav x-data="{ open: false, scrolled: false }"
     x-init="window.addEventListener('scroll', () => { scrolled = window.scrollY > 50 })"
     :class="{ 'bg-white shadow-lg': scrolled, 'bg-transparent': !scrolled }"
     class="fixed w-full z-50 transition-all duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16 lg:h-20">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="<?php echo e(route('home')); ?>" class="flex items-center">
                    <span class="text-xl lg:text-2xl font-bold text-gray-900">MarketPro</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:block">
                <div class="ml-10 flex items-baseline space-x-8">
                    <a href="<?php echo e(route('home')); ?>"
                       class="nav-link <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>">
                        Home
                    </a>
                    <a href="<?php echo e(route('services.index')); ?>"
                       class="nav-link <?php echo e(request()->routeIs('services.*') ? 'active' : ''); ?>">
                        Services
                    </a>
                    <a href="<?php echo e(route('case-studies.index')); ?>"
                       class="nav-link <?php echo e(request()->routeIs('case-studies.*') ? 'active' : ''); ?>">
                        Case Studies
                    </a>
                    <a href="<?php echo e(route('blog.index')); ?>"
                       class="nav-link <?php echo e(request()->routeIs('blog.*') ? 'active' : ''); ?>">
                        Blog
                    </a>
                    <a href="<?php echo e(route('about')); ?>"
                       class="nav-link <?php echo e(request()->routeIs('about') ? 'active' : ''); ?>">
                        About
                    </a>
                    <a href="<?php echo e(route('contact')); ?>"
                       class="nav-link <?php echo e(request()->routeIs('contact') ? 'active' : ''); ?>">
                        Contact
                    </a>
                </div>
            </div>

            <!-- CTA Button -->
            <div class="hidden lg:block">
                <a href="<?php echo e(route('contact')); ?>" class="btn-primary">
                    Get Started
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="lg:hidden">
                <button @click="open = !open"
                        class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                    <svg class="h-6 w-6" :class="{'hidden': open, 'block': !open }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <svg class="h-6 w-6" :class="{'block': open, 'hidden': !open }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="lg:hidden bg-white shadow-lg">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="<?php echo e(route('home')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>">Home</a>
            <a href="<?php echo e(route('services.index')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('services.*') ? 'active' : ''); ?>">Services</a>
            <a href="<?php echo e(route('case-studies.index')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('case-studies.*') ? 'active' : ''); ?>">Case Studies</a>
            <a href="<?php echo e(route('blog.index')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('blog.*') ? 'active' : ''); ?>">Blog</a>
            <a href="<?php echo e(route('about')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('about') ? 'active' : ''); ?>">About</a>
            <a href="<?php echo e(route('contact')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('contact') ? 'active' : ''); ?>">Contact</a>
            <div class="pt-4 pb-2">
                <a href="<?php echo e(route('contact')); ?>" class="btn-primary block w-full text-center">
                    Get Started
                </a>
            </div>
        </div>
    </div>
</nav>
<?php /**PATH C:\Users\<USER>\Desktop\New folder\marketpro-clone\resources\views/layouts/navigation.blade.php ENDPATH**/ ?>