<?php $__env->startSection('title', 'Home - Professional Marketing Solutions'); ?>
<?php $__env->startSection('meta_description', 'Transform your business with our comprehensive marketing solutions. Expert digital marketing, SEO, web development, and strategic consulting services.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1>Transform Your Business with <span style="color: #2563eb;">Expert Marketing</span></h1>
        <p>We help businesses grow through innovative digital marketing strategies, cutting-edge web development, and data-driven solutions that deliver real results.</p>
        <div>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">Get Started Today</a>
            <a href="<?php echo e(route('services.index')); ?>" class="btn btn-outline">Explore Services</a>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center" data-aos="fade-up">
                <div class="stats-counter mb-2" data-target="<?php echo e($stats['projects_completed']); ?>">0</div>
                <p class="text-gray-600 font-medium">Projects Completed</p>
            </div>
            <div class="text-center" data-aos="fade-up" data-aos-delay="100">
                <div class="stats-counter mb-2" data-target="<?php echo e($stats['happy_clients']); ?>">0</div>
                <p class="text-gray-600 font-medium">Happy Clients</p>
            </div>
            <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                <div class="stats-counter mb-2" data-target="<?php echo e($stats['years_experience']); ?>">0</div>
                <p class="text-gray-600 font-medium">Years Experience</p>
            </div>
            <div class="text-center" data-aos="fade-up" data-aos-delay="300">
                <div class="stats-counter mb-2" data-target="<?php echo e($stats['team_members']); ?>">0</div>
                <p class="text-gray-600 font-medium">Team Members</p>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="heading-lg mb-6" data-aos="fade-up">Our Services</h2>
            <p class="text-lead max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                Comprehensive marketing solutions designed to accelerate your business growth and maximize your digital presence.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $featuredServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="service-card" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                <div class="service-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-4 group-hover:text-blue-600 transition-colors duration-300">
                    <?php echo e($service->title); ?>

                </h3>
                <p class="text-gray-600 mb-6"><?php echo e($service->short_description); ?></p>
                <a href="<?php echo e(route('services.show', $service)); ?>" class="text-blue-600 font-medium hover:text-blue-700 transition-colors duration-300">
                    Learn More →
                </a>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-12" data-aos="fade-up">
            <a href="<?php echo e(route('services.index')); ?>" class="btn-primary">
                View All Services
            </a>
        </div>
    </div>
</section>

<!-- Case Studies Preview -->
<?php if($featuredCaseStudies->count() > 0): ?>
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="heading-lg mb-6" data-aos="fade-up">Success Stories</h2>
            <p class="text-lead max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                See how we've helped businesses like yours achieve remarkable growth and success.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $featuredCaseStudies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $caseStudy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card card-hover" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                <?php if($caseStudy->featured_image): ?>
                <img src="<?php echo e(asset('storage/' . $caseStudy->featured_image)); ?>" alt="<?php echo e($caseStudy->title); ?>" class="w-full h-48 object-cover">
                <?php endif; ?>
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-3"><?php echo e($caseStudy->title); ?></h3>
                    <p class="text-gray-600 mb-4"><?php echo e(Str::limit($caseStudy->overview, 120)); ?></p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500"><?php echo e($caseStudy->client_name); ?></span>
                        <a href="<?php echo e(route('case-studies.show', $caseStudy)); ?>" class="text-blue-600 font-medium hover:text-blue-700 transition-colors duration-300">
                            Read More →
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-12" data-aos="fade-up">
            <a href="<?php echo e(route('case-studies.index')); ?>" class="btn-outline">
                View All Case Studies
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Testimonials Section -->
<?php if($testimonials->count() > 0): ?>
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="heading-lg mb-6" data-aos="fade-up">What Our Clients Say</h2>
            <p class="text-lead max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                Don't just take our word for it. Here's what our satisfied clients have to say about working with us.
            </p>
        </div>

        <div class="swiper testimonials-swiper" data-aos="fade-up">
            <div class="swiper-wrapper">
                <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="swiper-slide">
                    <div class="testimonial-card">
                        <div class="testimonial-quote">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                            </svg>
                        </div>
                        <div class="mb-6">
                            <div class="flex text-yellow-400 mb-4">
                                <?php for($i = 0; $i < $testimonial->rating; $i++): ?>
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                <?php endfor; ?>
                            </div>
                            <p class="text-gray-700 text-lg leading-relaxed"><?php echo e($testimonial->content); ?></p>
                        </div>
                        <div class="flex items-center">
                            <?php if($testimonial->client_photo): ?>
                            <img src="<?php echo e(asset('storage/' . $testimonial->client_photo)); ?>" alt="<?php echo e($testimonial->client_name); ?>" class="w-12 h-12 rounded-full mr-4">
                            <?php else: ?>
                            <div class="w-12 h-12 bg-gray-300 rounded-full mr-4 flex items-center justify-center">
                                <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                </svg>
                            </div>
                            <?php endif; ?>
                            <div>
                                <h4 class="font-semibold text-gray-900"><?php echo e($testimonial->client_name); ?></h4>
                                <?php if($testimonial->client_position && $testimonial->client_company): ?>
                                <p class="text-sm text-gray-600"><?php echo e($testimonial->client_position); ?>, <?php echo e($testimonial->client_company); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="swiper-pagination mt-8"></div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Client Logos Section -->
<?php if($clients->count() > 0): ?>
<section class="py-12 bg-white">
    <div class="container-custom">
        <div class="text-center mb-12">
            <h3 class="text-2xl font-semibold text-gray-900 mb-4" data-aos="fade-up">Trusted by Leading Companies</h3>
            <p class="text-gray-600" data-aos="fade-up" data-aos-delay="200">We're proud to work with some of the most innovative companies in the industry.</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
            <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="flex items-center justify-center" data-aos="fade-up" data-aos-delay="<?php echo e($index * 50); ?>">
                <?php if($client->logo): ?>
                <img src="<?php echo e(asset('storage/' . $client->logo)); ?>" alt="<?php echo e($client->name); ?>" class="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300">
                <?php else: ?>
                <div class="h-12 w-24 bg-gray-200 rounded flex items-center justify-center">
                    <span class="text-xs text-gray-500 font-medium"><?php echo e($client->name); ?></span>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Blog Preview Section -->
<?php if($latestBlogPosts->count() > 0): ?>
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="heading-lg mb-6" data-aos="fade-up">Latest Insights</h2>
            <p class="text-lead max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                Stay updated with the latest trends, tips, and insights from our marketing experts.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $latestBlogPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <article class="blog-card" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                <?php if($post->featured_image): ?>
                <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="<?php echo e($post->title); ?>" class="blog-image">
                <?php endif; ?>
                <div class="blog-content">
                    <div class="blog-meta">
                        <span><?php echo e($post->formatted_published_date); ?></span>
                        <span class="mx-2">•</span>
                        <span><?php echo e($post->reading_time_text); ?></span>
                        <?php if($post->category): ?>
                        <span class="mx-2">•</span>
                        <span><?php echo e($post->category->name); ?></span>
                        <?php endif; ?>
                    </div>
                    <h3 class="blog-title">
                        <a href="<?php echo e(route('blog.show', $post)); ?>"><?php echo e($post->title); ?></a>
                    </h3>
                    <p class="blog-excerpt"><?php echo e($post->excerpt); ?></p>
                    <a href="<?php echo e(route('blog.show', $post)); ?>" class="blog-read-more">
                        Read More →
                    </a>
                </div>
            </article>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-12" data-aos="fade-up">
            <a href="<?php echo e(route('blog.index')); ?>" class="btn-outline">
                View All Articles
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="section-padding gradient-primary text-white">
    <div class="container-custom text-center">
        <h2 class="heading-lg mb-6 text-white" data-aos="fade-up">Ready to Transform Your Business?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90" data-aos="fade-up" data-aos-delay="200">
            Let's discuss how our proven strategies can help you achieve your business goals and drive sustainable growth.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="400">
            <a href="<?php echo e(route('contact')); ?>" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                Get Free Consultation
            </a>
            <a href="<?php echo e(route('services.index')); ?>" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                Explore Services
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Testimonials Swiper
    if (document.querySelector('.testimonials-swiper')) {
        new Swiper('.testimonials-swiper', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                },
                1024: {
                    slidesPerView: 3,
                }
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\New folder\marketpro-clone\resources\views/home.blade.php ENDPATH**/ ?>